import hashlib
import inspect
import logging
import os
import socketserver
import ssl
import tempfile
import time
import xmlrpc
import datetime
from ssl import SSLSocket, SSLObject
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography import x509
from cryptography.hazmat.primitives.serialization import load_pem_private_key
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes
from rich.logging import RichHandler
from file import File
from gps import GPSTest, GPS
from callback_service import CallbackService
from xmlrpc.server import SimpleXMLRPCServer, SimpleXMLRPCRequestHandler
from zeroconf import Zeroconf, ServiceInfo
import netifaces
from OpenSSL import crypto
import uuid
import threading

FORMAT = "%(message)s"
logging.basicConfig(
    level="NOTSET", format=FORMAT, datefmt="[%X]", handlers=[RichHandler(rich_tracebacks=True)]
)


def is_private_ip(ip):
    """
    Check if an IP address is in the private IP range.
    Args:
        ip (str): IP address to check.
    Returns:
        bool: True if the IP is private, False otherwise.
    """
    private_ranges = [
        ("10.0.0.0", "**************"),  # Class A private range
        ("**********", "**************"),  # Class B private range
        ("***********", "***************"),  # Class C private range
    ]

    def ip_to_int(ip):
        return sum(int(octet) << (8 * i) for i, octet in enumerate(reversed(ip.split('.'))))

    ip_int = ip_to_int(ip)

    for start, end in private_ranges:
        if ip_to_int(start) <= ip_int <= ip_to_int(end):
            return True
    return False


def get_local_private_ip():
    """
    Get the local IP address if it is a private IP.
    Returns:
        str: The local private IP address, or None if not found.
    """
    try:
        for iface in netifaces.interfaces():
            addresses = netifaces.ifaddresses(iface)
            if netifaces.AF_INET in addresses:
                for addr_info in addresses[netifaces.AF_INET]:
                    ip_addr = addr_info.get('addr')
                    if ip_addr and ip_addr != '127.0.0.1' and is_private_ip(ip_addr):
                        return ip_addr
    except Exception as e:
        logging.getLogger("get_local_ip").error(f"Error retrieving local IP: {e}")

    return "127.0.0.1"


class DistributedBus:
    _instances = {}

    def __new__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances = super().__new__(cls)
        return cls._instances

    def __init__(self, my_user_uuid="", my_node_uuid="", pre_registered_instance=None, debug=False):
        if pre_registered_instance is None:
            pre_registered_instance = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self.my_user_uuid = my_user_uuid if my_user_uuid else str(uuid.uuid4())
        self.my_node_uuid = my_node_uuid if my_node_uuid else str(uuid.uuid4())
        self.consumer_table = {}
        self.provider_table = {}
        self.servers = {}
        self.zeroconf = Zeroconf()

        class ThreadLocalClientStorage:

            def __init__(self):
                self.local = threading.local()

            def set_client(self, client_id, client_info):
                self.local.client_data = (client_id, client_info)

            def get_client(self):
                return getattr(self.local, 'client_data', (None, None))

            def get(self):
                return self.get_client()

        self.client_storage = ThreadLocalClientStorage()

        class EncryptedXMLRPCServer(SimpleXMLRPCServer):
            def __init__(self, addr, node_uuid, user_uuid, request_handler=SimpleXMLRPCRequestHandler,
                         logRequests=True, allow_none=True, encoding=None,
                         bind_and_activate=True, use_builtin_types=False, cert_file=None, keyfile=None,
                         client_cert_required=True):
                # TODO: keep the certificate in this stage
                # for each service, there's a instance of XMLRPC server
                self.identifier = uuid.uuid4()
                self.logger = logging.getLogger(self.__class__.__name__)
                self.logRequests = logRequests
                xmlrpc.server.SimpleXMLRPCDispatcher.__init__(self, allow_none, encoding, use_builtin_types)
                socketserver.TCPServer.__init__(self, addr, request_handler, bind_and_activate)
                self.sock_name = self.socket.getsockname()
                self.clients = {}
                self.lock = threading.Lock()
                self.client_timeout = 10


                if cert_file and keyfile:
                    self.context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
                    self.context.check_hostname = False
                    self.context.load_cert_chain(certfile=cert_file, keyfile=keyfile)

                    if client_cert_required:
                        self.context.verify_mode = ssl.CERT_REQUIRED
                        self.context.load_verify_locations("root_cert.pem")
                    else:
                        self.context.verify_mode = ssl.CERT_NONE

                    self.socket = self.context.wrap_socket(self.socket, server_side=True)

            def verify_client_cert(self, sock):
                try:
                    cert = sock.getpeercert()
                    not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                    if datetime.datetime.now() > not_after:
                        raise ssl.SSLError("Certificate expired")

                    return True
                except Exception as e:
                    logging.error(f"Certificate verification failed: {e}")
                    return False

            def get_request(self):
                sock, addr = super().get_request()
                self.logger.debug("get_request: sock: {}, ctx: {}, id: {}.".format(sock, sock.getpeercert(), self.identifier))
                if not self.verify_client_cert(sock):
                    sock.close()
                    raise ConnectionAbortedError("Invalid client certificate")

                client_id = self.get_client_id(sock)

                with self.lock:
                    self.clients[client_id] = {
                        'last_active': time.time(),
                        'address': addr,
                        'cert_info': sock.getpeercert() if isinstance(sock, SSLSocket) else None
                    }

                # threading.Thread(target=self.check_client_heartbeat, daemon=True).start()
                threading.Thread(target=self._heartbeat_loop, daemon=True).start() # Changed to _heartbeat_loop
                return sock, addr

            # def check_client_heartbeat(self):
            #     while True:
            #         time.sleep(self.client_timeout)
            #         current_time = time.time()
            #         with self.lock:
            #             to_delete = [cid for cid, info in self.clients.items()
            #                          if current_time - info['last_active'] > self.client_timeout]
            #             for cid in to_delete:
            #                 self.logger.warning("Client {} is not alive.".format(cid))
            #                 del self.clients[cid]

            def _heartbeat_loop(self):
                while True:
                    time.sleep(self.client_timeout)
                    now = time.time()
                    with self.lock:
                        stale = [cid for cid, info in self.clients.items() if now - info['last_active'] > self.client_timeout]
                        for cid in stale:
                            self.logger.warning("Client %s timed-out", cid)
                            self.clients.pop(cid, None)

            def get_client_id(self, sock):
                client_id_components = []

                try:
                    if isinstance(sock, (SSLSocket, SSLObject)):
                        der_cert = sock.getpeercert(binary_form=True)

                        if der_cert:
                            cert_fingerprint = hashlib.sha256(der_cert).hexdigest()[:16]
                            client_id_components.append("cert:{}".format(cert_fingerprint))

                            cert = crypto.load_certificate(crypto.FILETYPE_ASN1, der_cert)
                            pub_key = cert.get_pubkey()
                            pubkey_der = crypto.dump_publickey(crypto.FILETYPE_ASN1, pub_key)
                            pubkey_fingerprint = hashlib.sha256(pubkey_der).hexdigest()[:16]
                            client_id_components.append("pub:{}".format(pubkey_fingerprint))

                            subject = {x[0]: x[1] for x in cert.get_subject().get_components()}
                            cn = subject.get(b'CN', b'unknown').decode()
                            client_id_components.append("cn:{}".format(cn))

                except Exception as e:
                    self.logger.error(f"Certificate processing error: {e}")

                if not client_id_components:
                    try:
                        ip, port = sock.getpeername()
                        network_id = hashlib.sha256("{}:{}".format(ip, port).encode()).hexdigest()[:12]
                        client_id_components.append("net:{}".format(network_id))
                    except:
                        client_id_components.append("unk:{}".format(uuid.uuid4().hex[:8]))

                final_identifier = '|'.join(client_id_components)
                self.logger.debug("get_client_id: {}".format(final_identifier))
                return final_identifier

            @staticmethod
            def generate_certificates(cert_dir=None,
                                      root_ca_cert_path='root_cert.pem',
                                      root_ca_key_path='root_key.pem'):

                if cert_dir is None:
                    cert_dir = tempfile.mkdtemp()

                cert_file = os.path.join(cert_dir, "cert.pem")
                key_file = os.path.join(cert_dir, "key.pem")

                key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048
                )

                with open(root_ca_cert_path, 'rb') as f:
                    root_ca_cert = x509.load_pem_x509_certificate(f.read())

                with open(root_ca_key_path, 'rb') as f:
                    root_ca_key = load_pem_private_key(f.read(), password=None)

                subject = x509.Name([
                    x509.NameAttribute(NameOID.COMMON_NAME, "Android"),
                ])

                cert = (
                    x509.CertificateBuilder()
                    .subject_name(subject)
                    .issuer_name(root_ca_cert.subject)
                    .public_key(key.public_key())
                    .serial_number(1000)
                    .not_valid_before(datetime.datetime.now(datetime.timezone.utc))
                    .not_valid_after(datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=3650))
                    .sign(root_ca_key, hashes.SHA256())
                )

                with open(cert_file, "wb") as f:
                    f.write(cert.public_bytes(serialization.Encoding.PEM))

                with open(key_file, "wb") as f:
                    f.write(key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption()
                    ))

                self.logger.warning("Certificate stored in {}.".format(cert_dir))
                return cert_file, key_file
        self.rpc_server_class = EncryptedXMLRPCServer

        if debug:
            self.cert_file, self.keyfile = self.rpc_server_class.generate_certificates()
        else:
            self.cert_file = 'server_cert.pem'
            self.keyfile = 'server_key.pem'


        for pri in pre_registered_instance.keys():
            self.expose_as_rpc_server(pri, pre_registered_instance[pri])

    def register_mdns_service(self, zeroconf, port, service_name):
        service_info = ServiceInfo(
            type_="_distributed-bus._tcp.local.",
            name="{}.{}._distributed-bus._tcp.local.".format(service_name, self.my_node_uuid),
            port=port,
            addresses=[get_local_private_ip()],
            properties={"node_id": self.my_node_uuid, "user_id": self.my_user_uuid},
        )
        zeroconf.register_service(service_info)
        return service_info

    def wrap_client_aware_method(self, original_method):
        sig = inspect.signature(original_method)
        params = list(sig.parameters.values())

        client_args = {}
        if len(params) > 0:
            if params[0].name == 'self':
                offset = 1
            else:
                offset = 0

            param_names = [p.name for p in params[offset:]]

            if 'client_info' in param_names:
                client_args['client_info'] = True
            elif 'client_id' in param_names:
                client_args['client_id'] = True

            for field in ['network', 'cert', 'auth']:
                if field in param_names:
                    client_args[field] = field

        def wrapper(*args, **kwargs):
            client_info = self.client_storage.get()
            injected = {}

            if client_args.get('client_info'):
                injected['client_info'] = client_info
            if client_args.get('client_id'):
                injected['client_id'] = client_info.id
            for field in ['network', 'cert', 'auth']:
                if field in client_args:
                    injected[field] = getattr(client_info, field)

            try:
                return original_method(*args, **injected, **kwargs)
            except TypeError as e:
                if "unexpected keyword argument" in str(e):
                    return original_method(*args, **kwargs)
                raise

        return wrapper

    def expose_as_rpc_server(self, instance: object, module_name: str):
        # 获取类配置
        cls_config = getattr(instance.__class__, '__bus_config__', DistributedBus.ServiceConfig())

        # 创建沙盒代理
        if cls_config.isolated:
            instance = self.create_sandbox_proxy(instance, cls_config.allow_methods)

        self.servers[module_name] = self.rpc_server_class(('', 0), self.my_node_uuid, self.my_user_uuid, cert_file=self.cert_file, keyfile=self.keyfile)
        self.servers[module_name].register_instance(instance)
        service_name = module_name
        service = self.register_mdns_service(self.zeroconf, self.servers[module_name].socket.getsockname()[1], service_name)
        self.logger.warning('Service {0} listening on {1[0]}:{1[1]}'.format(module_name, self.servers[module_name].socket.getsockname()))
        self.zeroconf.send(self.zeroconf.generate_service_broadcast(service, 2))
        self.provider_table[module_name] = instance

    def serve_forever(self):
        for server in self.servers.values():
            threading.Thread(target=server.serve_forever, daemon=True).start()
        while True:
            pass

    class ServiceConfig:
        def __init__(self, isolated=False, allow_methods=None):
            self.isolated = isolated
            self.allow_methods = allow_methods or []

    def service_config(self, isolated=False, allow_methods=None):
        def decorator(cls):
            cls.__bus_config__ = DistributedBus.ServiceConfig(isolated, allow_methods)
            return cls

        return decorator

    def method_config(self, access_bus=False):
        def decorator(func):
            func.__bus_access__ = access_bus
            return func

        return decorator

    def create_sandbox_proxy(self, instance, allowed_methods):
        class SandboxProxy:
            def __init__(self, target, bus):
                self._target = target
                self._bus = bus
                self._allowed = allowed_methods

                for name in dir(target):
                    if (not name.startswith('_') and
                            (not allowed_methods or name in allowed_methods)):
                        self._wrap_method(name)

            def _wrap_method(self, name):
                original = getattr(self._target, name)
                if callable(original):
                    access_bus = getattr(original, '__bus_access__', False)

                    def wrapped(*args, **kwargs):
                        if access_bus:
                            return original(self._bus, *args, **kwargs)
                        else:
                            return original(*args, **kwargs)

                    setattr(self, name, wrapped)

        return SandboxProxy(instance, self)



# GUI memo application
# TODO:
# single node application runs both on android and macos
# popup windows -- single node first
# Next: split it into two node
# code should be almost same
def main():
    d = DistributedBus(my_user_uuid="0eec741f-74aa-45bb-94ce-657f9ec11fde", debug=True)
    gps = GPSTest()
    d.expose_as_rpc_server(gps, "GPS")

    #camera = CameraTest()
    #d.expose_as_rpc_server(camera, "Camera")

    file = File("buildozer.spec")
    d.expose_as_rpc_server(file, "File")

    # should register the callback function dynamically
    callback_svc = CallbackService()
    d.expose_as_rpc_server(callback_svc, "CallbackService")

    # Add memo service for distributed memo application
    from memo_service import MemoService
    memo_svc = MemoService()
    d.expose_as_rpc_server(memo_svc, "MemoService")

    d.serve_forever()

if __name__ == "__main__":
    main()
