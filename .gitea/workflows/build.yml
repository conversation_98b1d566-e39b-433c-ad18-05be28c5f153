name: Build and Release App

on:
  push:
    branches:
      - master

jobs:
  build_android:
    runs-on: ubuntu
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          . ~/XFrameVenv/bin/activate
          python -m pip install --upgrade pip
          pip install buildozer cython

      - name: Build Android APK
        run: |
          buildozer android debug

      - name: Upload APK
        uses: actions/upload-artifact@v2
        with:
          name: android-apk
          path: ./bin/*.apk

  build_ios:
    runs-on: macOS
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          conda activate XFrame
          python -m pip install --upgrade pip
          pip install buildozer cython distutils

      - name: Build iOS IPA
        run: |
          buildozer ios debug

      - name: Upload IPA
        uses: actions/upload-artifact@v2
        with:
          name: ios-ipa
          path: ./bin/*.ipa

  build_windows:
    runs-on: windows10
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller distutils

      - name: Build Windows exe
        run: |
          pyinstaller --onefile main.py

      - name: Upload exe
        uses: actions/upload-artifact@v2
        with:
          name: windows-exe
          path: ./dist/*.exe

  build_macos:
    runs-on: macOS
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          conda activate XFrame
          python -m pip install --upgrade pip
          pip install pyinstaller distutils

      - name: Build macOS app
        run: |
          pyinstaller --onefile main.py

      - name: Upload macOS app
        uses: actions/upload-artifact@v2
        with:
          name: macos-app
          path: ./dist/*.app

  build_linux:
    runs-on: ubuntu
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install build-essential
          . ~/XFrameVenv/bin/activate
          python -m pip install --upgrade pip
          pip install pyinstaller distutils

      - name: Build Linux binary
        run: |
          pyinstaller --onefile main.py

      - name: Upload Linux binary
        uses: actions/upload-artifact@v2
        with:
          name: linux-binary
          path: ./dist/*.bin

  release:
    needs: [build_android, build_ios, build_windows, build_macos, build_linux]
    runs-on: ubuntu
    steps:
      - name: Download APK
        uses: actions/download-artifact@v2
        with:
          name: android-apk

      - name: Download IPA
        uses: actions/download-artifact@v2
        with:
          name: ios-ipa

      - name: Download Windows exe
        uses: actions/download-artifact@v2
        with:
          name: windows-exe

      - name: Download macOS app
        uses: actions/download-artifact@v2
        with:
          name: macos-app

      - name: Download Linux binary
        uses: actions/download-artifact@v2
        with:
          name: linux-binary

      - name: Create Gitea release
        uses: docker://docker.pkg.github.com/docker/gitea-actions/release@v1
        env:
          GIT_TOKEN: ${{ secrets.GIT_TOKEN }}
        with:
          files: |
            ./android-apk/*.apk
            ./ios-ipa/*.ipa
            ./windows-exe/*.exe
            ./macos-app/*.app
            ./linux-binary/*.bin
          tag: ${{ github.sha }}
